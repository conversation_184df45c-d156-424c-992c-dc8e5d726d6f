import LayoutWeb from '@web/layout/index.vue'
export default {
  path: '/',
  redirect: {
    name: 'home',
  },
  component: LayoutWeb,
  children: [
    {
      path: '',
      name: 'home',
      meta: { title: 'nav.home' },
      component: () => import('@web/views/Home/index.vue'),
    },
    {
      path: 'about',
      name: 'about',
      meta: { title: 'nav.about' },
      component: () => import('@web/views/AboutView.vue'),
    },
    {
      path: 'shop-plans',
      name: 'shop-plans',
      meta: { title: 'nav.shop_plans' },
      component: () => import('@web/views/AboutView.vue'), // 临时使用AboutView
    },
    {
      path: 'my-esims',
      name: 'my-esims',
      meta: { title: 'nav.my_esims' },
      component: () => import('@web/views/MyEsims/index.vue'),
    },
    {
      path: 'esim-detail/:id',
      name: 'esim-detail',
      meta: { title: 'esimDetail.title' },
      component: () => import('@web/views/EsimDetail/index.vue'),
    },
    {
      path: 'login',
      name: 'login',
      meta: { title: 'nav.login' },
      component: () => import('@web/views/Login/Login.vue'),
    },
    {
      path: 'register',
      name: 'register',
      meta: { title: 'Register' },
      component: () => import('@web/views/Register/Register.vue'),
    },
    {
      path: 'forgot-password',
      name: 'forgot-password',
      meta: { title: 'Forgot Password' },
      component: () => import('@web/views/ForgotPassword/ForgotPassword.vue'),
    },
    {
      path: 'country-selection',
      name: 'country-selection',
      meta: { title: 'Select Country' },
      component: () => import('@web/views/CountrySelection/CountrySelection.vue'),
    },
  ],
}

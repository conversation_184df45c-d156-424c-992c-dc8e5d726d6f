<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()

// eSIM 详情数据
const esimDetail = ref({
  id: '',
  title: '5GB 3-Day for United States',
  data: '5GB',
  validity: '3 Days',
  planType: 'Data Only',
  expiry: '23 May 2025 | 02:54 (GMT)',
  coverage: 'United States, Canada, Mexico',
  description: [
    'The validity period starts when the eSIM connects to a mobile network in its coverage area. If you install the eSIM outside of the coverage area, you can connect to a network when you arrive.',
    'The validity period starts when the eSIM connects to a mobile network in its coverage area.',
    'If you install the eSIM outside of the coverage area, you can connect to a network when you arrive. The validity period starts when the...'
  ]
})

// 安装选项
const installationOptions = ref([
  {
    type: 'ios',
    name: 'iOS Device',
    icon: 'apple'
  },
  {
    type: 'android',
    name: 'Android Device',
    icon: 'android'
  }
])

onMounted(() => {
  // 从路由参数获取 eSIM ID
  const esimId = route.params.id
  if (esimId) {
    esimDetail.value.id = esimId as string
    // TODO: 根据 ID 加载具体的 eSIM 数据
  }
})

const handleInstallation = (type: string) => {
  console.log('Install for:', type)
  // TODO: 实现安装功能
}

const goBack = () => {
  router.back()
}
</script>

<template>
  <div class="esim-detail-page">
    <div class="container-box">
      <!-- 返回按钮 -->
      <div class="back-section">
        <button class="back-btn" @click="goBack">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
            <path d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"/>
          </svg>
          {{ t('common.back') }}
        </button>
      </div>

      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">{{ esimDetail.title }}</h1>
      </div>

      <!-- 套餐详情卡片 -->
      <div class="detail-card">
        <div class="detail-grid">
          <div class="detail-item">
            <span class="detail-label">{{ t('esimDetail.data') }}</span>
            <span class="detail-value">{{ esimDetail.data }}</span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">{{ t('esimDetail.validity') }}</span>
            <span class="detail-value">{{ esimDetail.validity }}</span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">{{ t('myEsims.planType') }}</span>
            <span class="detail-value">{{ esimDetail.planType }}</span>
          </div>
          
          <div class="detail-item">
            <span class="detail-label">{{ t('myEsims.expiry') }}</span>
            <span class="detail-value">{{ esimDetail.expiry }}</span>
          </div>
          
          <div class="detail-item full-width">
            <span class="detail-label">{{ t('myEsims.coverage') }}</span>
            <span class="detail-value">{{ esimDetail.coverage }}</span>
          </div>
        </div>
      </div>

      <!-- eSIM 安装区域 -->
      <div class="installation-section">
        <h2 class="section-title">{{ t('esimDetail.installation.title') }}</h2>
        
        <div class="installation-options">
          <div 
            v-for="option in installationOptions" 
            :key="option.type"
            class="installation-option"
          >
            <div class="option-content">
              <div class="option-icon">
                <svg v-if="option.icon === 'apple'" width="32" height="32" viewBox="0 0 32 32" fill="currentColor">
                  <path d="M24.769 8.054c-1.098.097-2.42.73-3.204 1.614-.686.784-1.274 2.03-1.078 3.225 1.176.078 2.42-.588 3.185-1.372.764-.882 1.254-2.03 1.097-3.467z"/>
                  <path d="M21.565 9.693c-1.764-.098-3.45.98-4.332.98-.882 0-2.254-.882-3.703-.862-1.901.02-3.665 1.098-4.645 2.803-1.98 3.43-.509 8.507 1.411 11.29.941 1.372 2.058 2.901 3.527 2.842 1.411-.059 1.941-.902 3.645-.902 1.705 0 2.176.902 3.684.862 1.529-.02 2.489-1.372 3.41-2.764 1.078-1.568 1.509-3.097 1.529-3.175-.039-.02-2.901-1.098-2.94-4.371-.02-2.744 2.254-4.058 2.352-4.117-1.274-1.862-3.254-2.078-3.938-2.156z"/>
                </svg>
                <svg v-else-if="option.icon === 'android'" width="32" height="32" viewBox="0 0 32 32" fill="currentColor">
                  <path d="M4.5 9.5v13c0 .828.672 1.5 1.5 1.5s1.5-.672 1.5-1.5v-13c0-.828-.672-1.5-1.5-1.5s-1.5.672-1.5 1.5z"/>
                  <path d="M24.5 9.5v13c0 .828.672 1.5 1.5 1.5s1.5-.672 1.5-1.5v-13c0-.828-.672-1.5-1.5-1.5s-1.5.672-1.5 1.5z"/>
                  <path d="M9 8h14v14.5c0 .828-.672 1.5-1.5 1.5h-1v2.5c0 .828-.672 1.5-1.5 1.5s-1.5-.672-1.5-1.5V24h-2v2.5c0 .828-.672 1.5-1.5 1.5s-1.5-.672-1.5-1.5V24h-1c-.828 0-1.5-.672-1.5-1.5V8z"/>
                  <path d="M20.071 3.929l1.414-1.414c.391-.391 1.023-.391 1.414 0s.391 1.023 0 1.414l-1.414 1.414c1.165.909 1.929 2.298 1.929 3.871 0 .276-.224.5-.5.5h-13c-.276 0-.5-.224-.5-.5 0-1.573.764-2.962 1.929-3.871l-1.414-1.414c-.391-.391-.391-1.023 0-1.414s1.023-.391 1.414 0l1.414 1.414c.909-.765 2.298-1.529 3.871-1.529s2.962.764 3.871 1.529z"/>
                  <circle cx="13" cy="6" r="1"/>
                  <circle cx="19" cy="6" r="1"/>
                </svg>
              </div>
              <span class="option-name">{{ option.name }}</span>
            </div>
            
            <button 
              class="btn btn-primary installation-btn"
              @click="handleInstallation(option.type)"
            >
              {{ t('esimDetail.installation.button') }}
            </button>
          </div>
        </div>
      </div>

      <!-- 描述区域 -->
      <div class="description-section">
        <h2 class="section-title">{{ t('esimDetail.description') }}</h2>
        
        <div class="description-content">
          <ol class="description-list">
            <li v-for="(item, index) in esimDetail.description" :key="index" class="description-item">
              {{ item }}
            </li>
          </ol>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.esim-detail-page {
  padding: 40px 0 80px;
  min-height: calc(100vh - 200px);
}

.back-section {
  margin-bottom: 30px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #6b7280;
  font-size: 16px;
  cursor: pointer;
  padding: 8px 0;
  transition: color 0.3s ease;
  
  &:hover {
    color: #374151;
  }
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 32px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.detail-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  margin-bottom: 40px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  &.full-width {
    grid-column: 1 / -1;
  }
}

.detail-label {
  font-size: 16px;
  color: #6b7280;
  font-weight: 500;
}

.detail-value {
  font-size: 16px;
  color: #111827;
  font-weight: 500;
  text-align: right;
}

.installation-section,
.description-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 24px 0;
}

.installation-options {
  display: grid;
  gap: 16px;
}

.installation-option {
  background: white;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.option-icon {
  width: 48px;
  height: 48px;
  background: #f3f4f6;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.option-name {
  font-size: 18px;
  font-weight: 500;
  color: #111827;
}

.installation-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #111827;
  color: white;
  
  &:hover {
    background: #374151;
  }
}

.description-content {
  background: white;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e5e7eb;
}

.description-list {
  margin: 0;
  padding-left: 20px;
  color: #6b7280;
  line-height: 1.6;
}

.description-item {
  font-size: 16px;
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .esim-detail-page {
    padding: 20px 0 60px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .detail-card {
    padding: 20px;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    
    .detail-value {
      text-align: left;
    }
  }
  
  .installation-option {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .installation-btn {
    width: 100%;
  }
}
</style>

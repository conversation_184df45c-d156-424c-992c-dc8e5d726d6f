<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'

interface Props {
  visible: boolean
  deviceType: 'ios' | 'android'
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { t } = useI18n()

// 当前选择的安装方式
const installMethod = ref<'qr' | 'manual'>('qr')

// eSIM 安装信息
const esimInfo = ref({
  smDpAddress: 'sm-prod.ondemandconnectivity.com',
  activationCode: 'FB890C197B97F1CB23F916699D0AEA186BD65 72D638C040594725AF805885E0'
})

// 设备版本选项
const deviceVersions = ref([
  { key: 'ios17', label: 'iOS 17 and later', active: true },
  { key: 'ios16', label: 'iOS 16', active: false },
  { key: 'ios15', label: 'iOS 15 and previous', active: false }
])

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const deviceTitle = computed(() => {
  return props.deviceType === 'ios' ? 'iOS Device' : 'Android Device'
})

// 方法
const closeModal = () => {
  emit('close')
}

const switchMethod = (method: 'qr' | 'manual') => {
  installMethod.value = method
}

const selectVersion = (versionKey: string) => {
  deviceVersions.value.forEach(version => {
    version.active = version.key === versionKey
  })
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    // TODO: 显示复制成功提示
    console.log('Copied to clipboard:', text)
  } catch (err) {
    console.error('Failed to copy:', err)
  }
}

const goToStepGuide = () => {
  // TODO: 跳转到详细步骤指南
  console.log('Go to step-by-step guide')
}
</script>

<template>
  <div v-if="modalVisible" class="modal-overlay" @click="closeModal">
    <div class="modal-container" @click.stop>
      <!-- 模态框头部 -->
      <div class="modal-header">
        <h2 class="modal-title">{{ deviceTitle }}</h2>
        <button class="close-btn" @click="closeModal">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- 安装方式切换 -->
      <div class="method-switch">
        <button 
          :class="['method-btn', { active: installMethod === 'qr' }]"
          @click="switchMethod('qr')"
        >
          {{ t('installModal.qrCode') }}
        </button>
        <button 
          :class="['method-btn', { active: installMethod === 'manual' }]"
          @click="switchMethod('manual')"
        >
          {{ t('installModal.manual') }}
        </button>
      </div>

      <!-- 警告信息 -->
      <div class="warning-section">
        <p class="warning-text">
          <span class="warning-label">{{ t('installModal.warning') }}</span>
          {{ t('installModal.warningText') }}
        </p>
      </div>

      <!-- iOS 版本选择 -->
      <div v-if="deviceType === 'ios'" class="version-section">
        <div class="version-tabs">
          <button
            v-for="version in deviceVersions"
            :key="version.key"
            :class="['version-tab', { active: version.active }]"
            @click="selectVersion(version.key)"
          >
            {{ version.label }}
          </button>
        </div>
      </div>

      <!-- QR Code 内容 -->
      <div v-if="installMethod === 'qr'" class="qr-section">
        <div class="qr-placeholder">
          <!-- 这里应该是实际的 QR Code -->
          <div class="qr-code">
            <svg width="200" height="200" viewBox="0 0 200 200" fill="none">
              <rect width="200" height="200" fill="white"/>
              <rect x="20" y="20" width="20" height="20" fill="black"/>
              <rect x="60" y="20" width="20" height="20" fill="black"/>
              <rect x="100" y="20" width="20" height="20" fill="black"/>
              <rect x="140" y="20" width="20" height="20" fill="black"/>
              <rect x="180" y="20" width="20" height="20" fill="black"/>
              <!-- 简化的 QR Code 样式 -->
            </svg>
          </div>
        </div>
      </div>

      <!-- Manual 内容 -->
      <div v-if="installMethod === 'manual'" class="manual-section">
        <!-- SM-DP+ Address -->
        <div class="info-item">
          <label class="info-label">{{ t('installModal.smDpAddress') }}</label>
          <div class="info-value">
            <span class="info-text">{{ esimInfo.smDpAddress }}</span>
            <button class="copy-btn" @click="copyToClipboard(esimInfo.smDpAddress)">
              {{ t('installModal.copy') }}
            </button>
          </div>
        </div>

        <!-- Activation Code -->
        <div class="info-item">
          <label class="info-label">{{ t('installModal.activationCode') }}</label>
          <div class="info-value">
            <span class="info-text">{{ esimInfo.activationCode }}</span>
            <button class="copy-btn" @click="copyToClipboard(esimInfo.activationCode)">
              {{ t('installModal.copy') }}
            </button>
          </div>
        </div>

        <!-- 安装说明 -->
        <div class="install-note">
          <p>{{ t('installModal.installNote') }}</p>
        </div>
      </div>

      <!-- 安装步骤 -->
      <div class="steps-section">
        <h3 class="steps-title">{{ t('installModal.steps.installTitle') }}</h3>
        <div class="steps-content">
          <ol class="steps-list">
            <li>{{ t('installModal.steps.step1') }}</li>
            <li>{{ t('installModal.steps.step2') }}</li>
            <li>{{ t('installModal.steps.step3') }}</li>
            <li>{{ t('installModal.steps.step4') }}</li>
            <li>{{ t('installModal.steps.step5') }}</li>
            <li>{{ t('installModal.steps.step6') }}</li>
          </ol>
        </div>

        <h3 class="steps-title">{{ t('installModal.steps.accessTitle') }}</h3>
        <div class="steps-content">
          <ol class="steps-list">
            <li>{{ t('installModal.steps.access1') }}</li>
            <li>{{ t('installModal.steps.access2') }}</li>
            <li>{{ t('installModal.steps.access3') }}</li>
          </ol>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="modal-footer">
        <button class="guide-btn" @click="goToStepGuide">
          {{ t('installModal.stepGuide') }}
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-container {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.modal-title {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.3s ease;
  
  &:hover {
    color: #374151;
  }
}

.method-switch {
  display: flex;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 4px;
  margin: 0 24px 24px;
}

.method-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &.active {
    background: #111827;
    color: white;
  }
}

.warning-section {
  margin: 0 24px 24px;
  padding: 16px;
  background: #fef2f2;
  border-radius: 8px;
  border-left: 4px solid #ef4444;
}

.warning-text {
  margin: 0;
  font-size: 14px;
  color: #7f1d1d;
  line-height: 1.5;
}

.warning-label {
  font-weight: 600;
  color: #dc2626;
}

.version-section {
  margin: 0 24px 24px;
}

.version-tabs {
  display: flex;
  gap: 8px;
}

.version-tab {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &.active {
    background: #10b981;
    color: white;
    border-color: #10b981;
  }
}

.qr-section {
  padding: 0 24px 24px;
  text-align: center;
}

.qr-placeholder {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.qr-code {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background: white;
}

.manual-section {
  padding: 0 24px 24px;
}

.info-item {
  margin-bottom: 20px;
}

.info-label {
  display: block;
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.info-value {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.info-text {
  flex: 1;
  font-size: 14px;
  color: #111827;
  word-break: break-all;
}

.copy-btn {
  padding: 6px 12px;
  background: #111827;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.3s ease;
  
  &:hover {
    background: #374151;
  }
}

.install-note {
  margin-top: 16px;
  padding: 12px;
  background: #f0f9ff;
  border-radius: 8px;
  
  p {
    margin: 0;
    font-size: 14px;
    color: #0369a1;
    line-height: 1.5;
  }
}

.steps-section {
  padding: 0 24px 24px;
}

.steps-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 16px 0;
}

.steps-content {
  margin-bottom: 24px;
}

.steps-list {
  margin: 0;
  padding-left: 20px;
  
  li {
    font-size: 14px;
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.modal-footer {
  padding: 24px;
  border-top: 1px solid #e5e7eb;
}

.guide-btn {
  width: 100%;
  padding: 16px;
  background: #111827;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
  
  &:hover {
    background: #374151;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-container {
    max-height: 95vh;
  }
  
  .modal-header {
    padding: 16px 16px 0;
  }
  
  .modal-title {
    font-size: 20px;
  }
  
  .method-switch,
  .warning-section,
  .version-section,
  .qr-section,
  .manual-section,
  .steps-section {
    margin-left: 16px;
    margin-right: 16px;
  }
  
  .modal-footer {
    padding: 16px;
  }
  
  .version-tabs {
    flex-direction: column;
  }
  
  .version-tab {
    text-align: center;
  }
}
</style>

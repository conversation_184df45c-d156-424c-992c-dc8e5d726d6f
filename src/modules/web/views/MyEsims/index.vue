<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { Icon } from '@/components/Icon'

const { t } = useI18n()
const router = useRouter()

// eSIM 数据 - 设置为空数组来测试空状态
const esimPlans = ref([
  // 有数据时的示例：
  // {
  //   id: 1,
  //   title: '5GB 3-Day for United States',
  //   planType: 'Data Only',
  //   coverage: 'United States, Canada, Mexi...',
  //   expiry: '23 May 2025 | 02:54 (GMT)'
  // },
  // {
  //   id: 2,
  //   title: '5GB 3-Day for United States',
  //   planType: 'Data Only',
  //   coverage: 'United States, Canada, Mexi...',
  //   expiry: '23 May 2025 | 02:54 (GMT)'
  // },
  // {
  //   id: 3,
  //   title: '5GB 3-Day for United States',
  //   planType: 'Data Only',
  //   coverage: 'United States, Canada, Mexi...',
  //   expiry: '23 May 2025 | 02:54 (GMT)'
  // }
])

// 计算属性：是否有数据
const hasPlans = computed(() => esimPlans.value.length > 0)

const handleDetails = (planId: number) => {
  console.log('View details for plan:', planId)
  // TODO: 实现查看详情功能
}

const handleBuyAgain = (planId: number) => {
  console.log('Buy again for plan:', planId)
  // TODO: 实现再次购买功能
}

const handleShopPlans = () => {
  router.push('/shop-plans')
}
</script>

<template>
  <div class="my-esims-page">
    <div class="container-box">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">{{ t('myEsims.title') }}</h1>
      </div>

      <!-- eSIM 卡片列表 -->
      <div v-if="hasPlans" class="esim-cards-container">
        <div
          v-for="plan in esimPlans"
          :key="plan.id"
          class="esim-card"
        >
          <!-- 卡片图标 -->
          <div class="card-icon">
            <div class="esim-chip">
              <div class="chip-outer">
                <div class="chip-inner">
                  <span class="chip-text">eSIM</span>
                </div>
                <div class="chip-contacts">
                  <div class="contact-row">
                    <div class="contact"></div>
                    <div class="contact"></div>
                    <div class="contact"></div>
                    <div class="contact"></div>
                  </div>
                  <div class="contact-row">
                    <div class="contact"></div>
                    <div class="contact"></div>
                    <div class="contact"></div>
                    <div class="contact"></div>
                  </div>
                  <div class="contact-row">
                    <div class="contact"></div>
                    <div class="contact"></div>
                    <div class="contact"></div>
                    <div class="contact"></div>
                  </div>
                  <div class="contact-row">
                    <div class="contact"></div>
                    <div class="contact"></div>
                    <div class="contact"></div>
                    <div class="contact"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 卡片标题 -->
          <h3 class="card-title">{{ plan.title }}</h3>
          
          <!-- 分隔线 -->
          <div class="divider"></div>
          
          <!-- 卡片详情 -->
          <div class="card-details">
            <div class="detail-row">
              <span class="detail-label">{{ t('myEsims.planType') }}</span>
              <span class="detail-value">{{ plan.planType }}</span>
            </div>
            
            <div class="detail-row">
              <span class="detail-label">{{ t('myEsims.coverage') }}</span>
              <span class="detail-value">{{ plan.coverage }}</span>
            </div>
            
            <div class="detail-row">
              <span class="detail-label">{{ t('myEsims.expiry') }}</span>
              <span class="detail-value">{{ plan.expiry }}</span>
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="divider"></div>

          <!-- 操作按钮 -->
          <div class="card-actions">
            <button 
              class="btn btn-primary"
              @click="handleDetails(plan.id)"
            >
              {{ t('myEsims.details') }}
            </button>
            
            <button 
              class="btn btn-secondary"
              @click="handleBuyAgain(plan.id)"
            >
              {{ t('myEsims.buyAgain') }}
            </button>
          </div>
        </div>
      </div>

      <!-- 空状态显示 -->
      <div v-else class="empty-state">
        <div class="empty-icon">
           <Icon icon="svg-icon:no-list" :size="240"></Icon>
        </div>
        <h2 class="empty-title">{{ t('myEsims.empty.title') }}</h2>
        <p class="empty-description">{{ t('myEsims.empty.description') }}</p>
        <button class="btn btn-primary shop-btn" @click="handleShopPlans">
          {{ t('myEsims.empty.shopPlans') }}
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.my-esims-page {
  padding: 60px 0;
  min-height: calc(100vh - 200px);
  background: var(--bg-color);
}

.page-header {
  text-align: center;
  margin-bottom: 60px;
}

.page-title {
  font-size: 48px;
  font-weight: bold;
  color: var(--primary-color);
  margin: 0;
}

.esim-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 30px;
  margin-bottom: 100px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.esim-card {
  background: white;
  border-radius: 24px;
  padding: 40px 30px;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #f0f0f0;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }
}

.card-icon {
  text-align: center;
  margin-bottom: 30px;
}

.esim-chip {
  display: inline-block;
  position: relative;
}

.chip-outer {
  width: 90px;
  height: 90px;
  background: #f8f9fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #e9ecef;
  position: relative;
}

.chip-inner {
  width: 60px;
  height: 60px;
  background: #ffffff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #dee2e6;
  position: relative;
}

.chip-text {
  font-size: 10px;
  font-weight: bold;
  color: #6c757d;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.chip-contacts {
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  bottom: 8px;
}

.contact-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.contact {
  width: 3px;
  height: 3px;
  background: #adb5bd;
  border-radius: 50%;
}

.card-title {
  font-size: 22px;
  font-weight: 600;
  color: #212529;
  margin: 0 0 24px 0;
  text-align: center;
  line-height: 1.3;
}

.divider {
  height: 1px;
  background: #e9ecef;
  margin: 24px 0;
}

.card-details {
  margin-bottom: 24px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  font-size: 16px;
  color: #495057;
  font-weight: 500;
  flex-shrink: 0;
}

.detail-value {
  font-size: 16px;
  color: #adb5bd;
  font-weight: 400;
  text-align: right;
  max-width: 60%;
  line-height: 1.4;
}

.card-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.btn {
  padding: 16px 24px;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  
  &.btn-primary {
    background: #212529;
    color: white;
    
    &:hover {
      background: #495057;
    }
  }
  
  &.btn-secondary {
    background: transparent;
    color: #212529;
    border: 2px solid #212529;
    
    &:hover {
      background: #212529;
      color: white;
    }
  }
}

// 空状态样式
.empty-state {
  text-align: center;
  padding: 80px 20px;
  max-width: 600px;
  margin: 0 auto;
}

.empty-icon {
  margin-bottom: 40px;

  img {
    width: 200px;
    height: 200px;
    opacity: 0.8;
  }
}

.empty-title {
  font-size: 32px;
  font-weight: 600;
  color: #212529;
  margin: 0 0 20px 0;
}

.empty-description {
  font-size: 18px;
  color: #6c757d;
  margin: 0 0 40px 0;
  line-height: 1.6;
}

.shop-btn {
  padding: 16px 40px;
  font-size: 18px;
  min-width: 200px;
}

// 应用下载区域样式
.app-download-section {
  background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
  border-radius: 20px;
  padding: 60px 40px;
  margin-top: 80px;
}

.download-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.download-title {
  font-size: 36px;
  font-weight: bold;
  color: #212529;
  margin: 0 0 20px 0;
}

.download-description {
  font-size: 18px;
  color: #495057;
  margin: 0 0 30px 0;
  line-height: 1.6;
}

.download-buttons {
  display: flex;
  gap: 20px;
}

.download-btn {
  display: inline-block;

  img {
    height: 50px;
    transition: transform 0.3s ease;
  }

  &:hover img {
    transform: scale(1.05);
  }
}

.download-image {
  text-align: center;

  img {
    max-width: 100%;
    height: auto;
    max-height: 400px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .my-esims-page {
    padding: 40px 0;
  }

  .page-title {
    font-size: 36px;
  }

  .esim-cards-container {
    grid-template-columns: 1fr;
    gap: 20px;
    margin-bottom: 60px;
  }

  .esim-card {
    padding: 30px 20px;
  }

  .download-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .download-title {
    font-size: 28px;
  }

  .app-download-section {
    padding: 40px 20px;
  }

  .download-buttons {
    justify-content: center;
  }

  .empty-state {
    padding: 60px 20px;
  }

  .empty-icon img {
    width: 150px;
    height: 150px;
  }

  .empty-title {
    font-size: 24px;
  }

  .empty-description {
    font-size: 16px;
  }

  .shop-btn {
    padding: 14px 30px;
    font-size: 16px;
    min-width: 180px;
  }
}
</style>

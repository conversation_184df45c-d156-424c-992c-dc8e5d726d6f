<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// eSIM 数据
const esimPlans = ref([
  {
    id: 1,
    title: '5GB 3-Day for United States',
    planType: 'Data Only',
    coverage: 'United States, Canada, Mexico',
    expiry: '23 May 2025 | 02:54 (GMT)',
    icon: '/src/assets/images/esim-icon.svg'
  },
  {
    id: 2,
    title: '5GB 3-Day for United States',
    planType: 'Data Only',
    coverage: 'United States, Canada, Mexico',
    expiry: '23 May 2025 | 02:54 (GMT)',
    icon: '/src/assets/images/esim-icon.svg'
  },
  {
    id: 3,
    title: '5GB 3-Day for United States',
    planType: 'Data Only',
    coverage: 'United States, Canada, Mexico',
    expiry: '23 May 2025 | 02:54 (GMT)',
    icon: '/src/assets/images/esim-icon.svg'
  }
])

const handleDetails = (planId: number) => {
  console.log('View details for plan:', planId)
  // TODO: 实现查看详情功能
}

const handleBuyAgain = (planId: number) => {
  console.log('Buy again for plan:', planId)
  // TODO: 实现再次购买功能
}
</script>

<template>
  <div class="my-esims-page">
    <div class="container-box">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">{{ t('myEsims.title') }}</h1>
      </div>

      <!-- eSIM 卡片列表 -->
      <div class="esim-cards-container">
        <div 
          v-for="plan in esimPlans" 
          :key="plan.id" 
          class="esim-card"
        >
          <!-- 卡片图标 -->
          <div class="card-icon">
            <div class="icon-circle">
              <img src="/src/assets/images/esim-placeholder.svg" alt="eSIM" />
            </div>
          </div>

          <!-- 卡片内容 -->
          <div class="card-content">
            <h3 class="card-title">{{ plan.title }}</h3>
            
            <div class="card-details">
              <div class="detail-row">
                <span class="detail-label">{{ t('myEsims.planType') }}</span>
                <span class="detail-value">{{ plan.planType }}</span>
              </div>
              
              <div class="detail-row">
                <span class="detail-label">{{ t('myEsims.coverage') }}</span>
                <span class="detail-value">{{ plan.coverage }}</span>
              </div>
              
              <div class="detail-row">
                <span class="detail-label">{{ t('myEsims.expiry') }}</span>
                <span class="detail-value">{{ plan.expiry }}</span>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card-actions">
              <button 
                class="btn btn-primary"
                @click="handleDetails(plan.id)"
              >
                {{ t('myEsims.details') }}
              </button>
              
              <button 
                class="btn btn-secondary"
                @click="handleBuyAgain(plan.id)"
              >
                {{ t('myEsims.buyAgain') }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 应用下载推广区域 -->
      <div class="app-download-section">
        <div class="download-content">
          <div class="download-text">
            <h2 class="download-title">{{ t('myEsims.downloadApp.title') }}</h2>
            <p class="download-description">
              {{ t('myEsims.downloadApp.description') }}
            </p>
            
            <div class="download-buttons">
              <a href="#" class="download-btn app-store">
                <img src="/src/assets/images/app-store.svg" alt="Download on App Store" />
              </a>
              <a href="#" class="download-btn google-play">
                <img src="/src/assets/images/google-play.svg" alt="Get it on Google Play" />
              </a>
            </div>
          </div>
          
          <div class="download-image">
            <img src="/src/assets/images/phone-mockup.png" alt="GlocalMe App" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.my-esims-page {
  padding: 60px 0;
  min-height: calc(100vh - 200px);
}

.page-header {
  text-align: center;
  margin-bottom: 60px;
}

.page-title {
  font-size: 48px;
  font-weight: bold;
  color: var(--primary-color);
  margin: 0;
}

.esim-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 100px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.esim-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  }
}

.card-icon {
  text-align: center;
  margin-bottom: 20px;
}

.icon-circle {
  width: 80px;
  height: 80px;
  background: #f5f5f5;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  img {
    width: 40px;
    height: 40px;
  }
}

.card-content {
  text-align: center;
}

.card-title {
  font-size: 20px;
  font-weight: bold;
  color: var(--primary-color);
  margin: 0 0 20px 0;
}

.card-details {
  margin-bottom: 30px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 0 10px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  color: var(--primary-color);
  font-weight: 500;
  text-align: right;
  max-width: 60%;
}

.card-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &.btn-primary {
    background: var(--primary-color);
    color: white;
    
    &:hover {
      background: var(--base-color);
    }
  }
  
  &.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    
    &:hover {
      background: var(--primary-color);
      color: white;
    }
  }
}

.app-download-section {
  background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
  border-radius: 20px;
  padding: 60px 40px;
  margin-top: 80px;
}

.download-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.download-title {
  font-size: 36px;
  font-weight: bold;
  color: var(--primary-color);
  margin: 0 0 20px 0;
}

.download-description {
  font-size: 18px;
  color: var(--primary-color);
  margin: 0 0 30px 0;
  line-height: 1.6;
}

.download-buttons {
  display: flex;
  gap: 20px;
}

.download-btn {
  display: inline-block;
  
  img {
    height: 50px;
    transition: transform 0.3s ease;
  }
  
  &:hover img {
    transform: scale(1.05);
  }
}

.download-image {
  text-align: center;
  
  img {
    max-width: 100%;
    height: auto;
    max-height: 400px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .my-esims-page {
    padding: 40px 0;
  }
  
  .page-title {
    font-size: 36px;
  }
  
  .esim-cards-container {
    grid-template-columns: 1fr;
    gap: 20px;
    margin-bottom: 60px;
  }
  
  .esim-card {
    padding: 20px;
  }
  
  .download-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  
  .download-title {
    font-size: 28px;
  }
  
  .app-download-section {
    padding: 40px 20px;
  }
}
</style>

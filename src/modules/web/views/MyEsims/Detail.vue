<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import InstallationModal from '@web/views/MyEsims/Components/InstallationModal.vue'
import { Icon } from '@/components/Icon'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()

// eSIM 详情数据
const esimDetail = ref({
  id: '',
  title: '5GB 3-Day for United States',
  data: '5GB',
  validity: '3 Days',
  planType: 'Data Only',
  expiry: '23 May 2025 | 02:54 (GMT)',
  coverage: 'United States, Canada, Mexico',
  description: [
    'The validity period starts when the eSIM connects to a mobile network in its coverage area. If you install the eSIM outside of the coverage area, you can connect to a network when you arrive.',
    'The validity period starts when the eSIM connects to a mobile network in its coverage area.',
    'If you install the eSIM outside of the coverage area, you can connect to a network when you arrive. The validity period starts when the...'
  ]
})

// installDialog
const installDialog = reactive({
  visible: false,
  deviceType: 'ios'
})
// 安装选项
const installationOptions = ref([
  {
    type: 'ios',
    name: 'iOS Device',
    icon: 'apple'
  },
  {
    type: 'android',
    name: 'Android Device',
    icon: 'android'
  }
])

onMounted(() => {
  // 从路由参数获取 eSIM ID
  const esimId = route.params.id
  if (esimId) {
    esimDetail.value.id = esimId as string
    // TODO: 根据 ID 加载具体的 eSIM 数据
  }
})

const handleInstallation = (type: string) => {
  console.log('Install for:', type)
  // TODO: 实现安装功能
  installDialog.visible = true
  installDialog.deviceType = type as 'ios' | 'android'
}

const goBack = () => {
  router.back()
}
</script>

<template>
  <div class="esim-detail-page">
    <div class="container-box">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">{{ esimDetail.title }}</h1>
      </div>

      <!-- 套餐详情卡片 -->
      <div class="detail-card">
        <div class="detail-grid">
          <div class="detail-item">
            <span class="detail-label">{{ t('esimDetail.data') }}</span>
            <span class="detail-value">{{ esimDetail.data }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">{{ t('esimDetail.validity') }}</span>
            <span class="detail-value">{{ esimDetail.validity }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">{{ t('myEsims.planType') }}</span>
            <span class="detail-value">{{ esimDetail.planType }}</span>
          </div>

          <div class="detail-item">
            <span class="detail-label">{{ t('myEsims.expiry') }}</span>
            <span class="detail-value">{{ esimDetail.expiry }}</span>
          </div>

          <div class="detail-item full-width">
            <span class="detail-label">{{ t('myEsims.coverage') }}</span>
            <span class="detail-value">{{ esimDetail.coverage }}</span>
          </div>
        </div>
      </div>

      <!-- eSIM 安装区域 -->
      <div class="installation-section">
        <h2 class="section-title">{{ t('esimDetail.installation.title') }}</h2>

        <div class="installation-options">
          <div
            v-for="option in installationOptions"
            :key="option.type"
            class="installation-option"
          >
            <div class="option-content">
              <div class="option-icon">
                <Icon v-if="option.icon === 'apple'" icon="svg-icon:apple-device" :size="32"></Icon>
                 <Icon v-if="option.icon === 'android'" icon="svg-icon:androd-device" :size="32"></Icon>
              </div>
              <span class="option-name">{{ option.name }}</span>
            </div>

            <button
              class="btn btn-primary installation-btn"
              @click="handleInstallation(option.type)"
            >
              {{ t('esimDetail.installation.button') }}
            </button>
          </div>
        </div>
      </div>

      <!-- 描述区域 -->
      <div class="description-section">
        <h2 class="section-title">{{ t('esimDetail.description') }}</h2>

        <div class="description-content">
          <ol class="description-list">
            <li v-for="(item, index) in esimDetail.description" :key="index" class="description-item">
              {{ item }}
            </li>
          </ol>
        </div>
      </div>
    </div>
  </div>
  <InstallationModal v-model:visible="installDialog.visible" :device-type="installDialog.deviceType"></InstallationModal>
</template>

<style scoped lang="scss">
.esim-detail-page {
  padding: 40px 0 80px;
  min-height: calc(100vh - 200px);
}

.back-section {
  margin-bottom: 30px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #6b7280;
  font-size: 16px;
  cursor: pointer;
  padding: 8px 0;
  transition: color 0.3s ease;

  &:hover {
    color: #374151;
  }
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 32px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.detail-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  margin-bottom: 40px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;

  &.full-width {
    grid-column: 1 / -1;
  }
}

.detail-label {
  font-size: 16px;
  color: #6b7280;
  font-weight: 500;
}

.detail-value {
  font-size: 16px;
  color: #111827;
  font-weight: 500;
  text-align: right;
}

.installation-section,
.description-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 24px 0;
}

.installation-options {
  display: grid;
  gap: 16px;
}

.installation-option {
  background: white;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.option-icon {
  width: 48px;
  height: 48px;
  background: #f3f4f6;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.option-name {
  font-size: 18px;
  font-weight: 500;
  color: #111827;
}

.installation-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #111827;
  color: white;

  &:hover {
    background: #374151;
  }
}

.description-content {
  background: white;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e5e7eb;
}

.description-list {
  margin: 0;
  padding-left: 20px;
  color: #6b7280;
  line-height: 1.6;
}

.description-item {
  font-size: 16px;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .esim-detail-page {
    padding: 20px 0 60px;
  }

  .page-title {
    font-size: 24px;
  }

  .detail-card {
    padding: 20px;
  }

  .detail-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;

    .detail-value {
      text-align: left;
    }
  }

  .installation-option {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .installation-btn {
    width: 100%;
  }
}
</style>

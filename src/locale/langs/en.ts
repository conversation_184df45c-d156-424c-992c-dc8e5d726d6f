export default {
  common: {
    search: 'Search',
    confirm: 'Confirm',
    cancel: 'Cancel',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    failed: 'Failed',
    retry: 'Retry',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    prev: 'Previous',
    finish: 'Finish',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',
    add: 'Add',
    submit: 'Submit',
    reset: 'Reset',
  },
  app: {
    title: 'esim store',
  },
  header: {
    msg: 'Hello!',
    desc: "You've successfully created a project with{0} + {1}. What's next?",
    desc_vite: 'Vite',
    desc_vue: 'Vue 3',
    logo: 'LocalMe',
  },
  search: {
    placeholder: 'Search for your destination worldwide',
  },
  nav: {
    home: 'Home',
    about: 'About',
    mobile: 'Mobile',
    shop_plans: 'Shop Plans',
    my_esims: 'My eSIMs',
    about_us: 'About us',
    login: 'Login',
  },
  login: {
    title: "What's your email address?",
    email_label: 'Email',
    email_placeholder: 'Please enter',
    continue: 'Continue',
    agreement_text: 'By logging in or creating an account, you agree to',
    user_agreement: 'User Agreement',
    and: 'and',
    privacy_policy: 'Privacy Policy',
    or: 'OR',
    continue_with_google: 'Continue with Google',
    continue_with_apple: 'Continue with Apple',
    continue_with_mobile: 'Continue with Mobile phone',
    password_label: 'Password',
    forgot_password: 'Forget Password?',
    login: 'Log In',
  },
  register: {
    title: 'Create your account',
    password_label: 'Password',
    password_placeholder: 'Please enter password',
    confirm_password_label: 'Retype Password',
    confirm_password_placeholder: 'Please enter password again',
    register_button: 'Register',
    registering: 'Registering...',
    email_error: 'Please enter a valid email address',
    password_error:
      'Password must be 6-20 characters long and include at least two of the following: letters, numbers, or symbols. It also cannot be one you’ve used before.',
    password_mismatch: 'Passwords do not match',
    register_success: 'Registration successful!',
    register_failed: 'Registration failed, please try again',
    user_exists: 'User already exists, please login directly',
    google_register_developing: 'Google registration feature is under development',
    apple_register_developing: 'Apple registration feature is under development',
    go_to_login: 'Already have an account? Sign in',
    select_residence: 'Select your place of residence',
    country_region: 'Country / region',
    sign_up: 'Sign up',
    select_country_placeholder: 'Please select country/region',
  },
  forgot_password: {
    title: 'Forgot Password',
    back: 'Back',
    reset_title: 'Reset Password',
    subtitle: 'We will send a verification code to your email, please enter the 6-digit code',
    email_label: 'Email',
    email_placeholder: '<EMAIL>',
    verification_code_label: 'Verification Code',
    verification_code_placeholder: 'Please enter 6-digit code',
    new_password_label: 'New Password',
    new_password_placeholder: 'Please enter new password',
    confirm_new_password_label: 'Confirm Password',
    confirm_new_password_placeholder: 'Please enter new password again',
    send_code: 'Send Code',
    sending: 'Sending...',
    resetting: 'Resetting...',
    save_button: 'Save',
    code_error: 'Please enter 6-digit verification code',
    password_requirements:
      "Password must be 6-20 characters long and include at least two of the following: letters, numbers, or symbols. It also cannot be one you've used before.",
    remember_password: 'Remember your password?',
    back_to_login: 'Back to Login',
    code_sent_success: 'Verification code sent',
    code_sent_failed: 'Failed to send verification code',
    reset_success: 'Password reset successful',
    reset_failed: 'Password reset failed',
  },
  home: {
    title: 'Connect to Ideal Network at',
    title_highlight: 'Affordable Rates',
    adventure: 'Bloom into Adventure with 20% Off!',
    share_friend: 'Bring a friend and Enjoy USD 3 off!',
    popular: {
      title: 'POPULAR DESTINATIONS',
      local: 'Local eSIMs',
      regional: 'Regional eSIMs',
    },
    download_app: {
      title: 'DOWNLOAD THE GLOCALME APP',
      description:
        'Download the GlocalMe app to purchase, manage, and top up your plans anytime, anywhere!',
      app_store: 'Download on the App Store',
      google_play: 'Get it on Google Play',
    },
    refer_friend: {
      title: 'BRING A FRIEND AND',
      highlight: 'ENJOY $5 OFF',
      button: 'Refer a Friend',
    },
    user_reviews: {
      title: 'HEAR FROM GLOCALME USERS!',
    },
    how_it_works: {
      title: 'HOW DOES GLOCALME WORK?',
      steps: {
        step1: {
          step: 'STEP 01',
          title: 'Login',
          description: 'Install the GlocalMe app and create a new account',
        },
        step2: {
          step: 'STEP 02',
          title: 'Shop',
          description:
            'Navigate to Mall > eSIM & Travel SIM > eSIM Standard. Choose your destination and plan',
        },
        step3: {
          step: 'STEP 03',
          title: 'Activation',
          description:
            'Access Order Details > eSIM installation instructions for your QR code or activation code. Ensure network connectivity and follow the steps to complete eSIM activation.',
        },
        step4: {
          step: 'STEP 04',
          title: 'Access',
          description:
            'Enable the "Data Roaming" toggle for your new eSIM plan to connect to the network',
        },
      },
    },
    exclusive: {
      tag: 'CEO EXCLUSIVE',
      title: '100GB 365-DAY',
      subtitle: 'PACKAGE FOR GLOBAL',
      button: 'Learn More',
    },
  },
  footer: {
    start_travel: 'Start your travel with GlocalMe!',
    stay_connected: 'Stay Connected',
    shop_plans: 'Shop Plans',
    refer_friend: 'Refer a Friend',
    my_promos: 'My Promos',
    about_us: 'About us',
    about_glocalme: 'About GlocalMe',
    terms: 'Terms & Conditions',
    privacy_policy: 'Privacy Policy',
    contact_us: 'Contact Us',
    follow_us: 'Follow us',
    scan_download_app: 'Scan and download the app',
  },
  myEsims: {
    title: 'My eSIMs',
    planType: 'Plan type',
    coverage: 'Coverage',
    expiry: 'Expiry',
    details: 'Details',
    buyAgain: 'Buy again',
    downloadApp: {
      title: 'DOWNLOAD THE GLOCALME APP',
      description: 'Download the GlocalMe app to purchase, manage, and top up your plans anytime, anywhere!',
    },
  },
  copyright: {
    text: `Copyright{'@'}2025 GlocalMe uCloudlink Group Inc.`,
  },
}

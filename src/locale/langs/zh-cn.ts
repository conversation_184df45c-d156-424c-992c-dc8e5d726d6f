export default {
  common: {
    search: '搜索',
    confirm: '确认',
    cancel: '取消',
    loading: '加载中...',
    error: '出错了',
    success: '成功',
    failed: '失败',
    retry: '重试',
    close: '关闭',
    back: '返回',
    next: '下一步',
    prev: '上一步',
    finish: '完成',
    save: '保存',
    edit: '编辑',
    delete: '删除',
    add: '添加',
    submit: '提交',
    reset: '重置',
  },

  app: {
    title: 'esim 商场',
  },
  header: {
    msg: '你好！',
    desc: '你已经用{0}+{1}成功的创建了项目。接下来是什么？',
    desc_vite: 'Vite',
    desc_vue: 'Vue 3',
    logo: 'LocalMe',
  },
  search: {
    placeholder: '搜索您的全球目的地',
  },
  nav: {
    home: '首页',
    about: '关于',
    mobile: '移动端',
    shop_plans: '购买套餐',
    my_esims: '我的eSIM',
    about_us: '关于我们',
    login: '登录',
  },
  login: {
    title: '您的邮箱地址是什么？',
    email_label: '邮箱',
    email_placeholder: '请输入',
    continue: '继续',
    agreement_text: '通过登录或创建账户，您同意',
    user_agreement: '用户协议',
    and: '和',
    privacy_policy: '隐私政策',
    or: '或',
    continue_with_google: '使用Google继续',
    continue_with_apple: '使用Apple继续',
    continue_with_mobile: '使用手机号继续',
  },
  register: {
    title: '创建您的账户',
    password_label: '密码',
    password_placeholder: '请输入密码',
    confirm_password_label: '确认密码',
    confirm_password_placeholder: '请再次输入密码',
    register_button: '注册',
    registering: '注册中...',
    email_error: '请输入正确的邮箱地址',
    password_error: '密码必须6-20位，包含字母、数字和符号',
    password_mismatch: '两次输入的密码不一致',
    register_success: '注册成功！',
    register_failed: '注册失败，请重试',
    user_exists: '用户已存在，请直接登录',
    google_register_developing: 'Google 注册功能开发中',
    apple_register_developing: 'Apple 注册功能开发中',
    go_to_login: '已有账户？去登录',
    select_residence: '选择您的居住地',
    country_region: '国家/地区',
    sign_up: '注册',
    select_country_placeholder: '请选择国家/地区',
  },
  forgot_password: {
    title: '忘记密码',
    back: '返回',
    reset_title: '重置密码',
    subtitle: '我们将发送验证码到您的邮箱，请输入6位验证码',
    email_label: '邮箱',
    email_placeholder: '<EMAIL>',
    verification_code_label: '验证码',
    verification_code_placeholder: '请输入6位验证码',
    new_password_label: '新密码',
    new_password_placeholder: '请输入新密码',
    confirm_new_password_label: '确认密码',
    confirm_new_password_placeholder: '请再次输入新密码',
    send_code: '发送验证码',
    sending: '发送中...',
    resetting: '重置中...',
    save_button: '保存',
    code_error: '请输入6位数字验证码',
    password_requirements:
      '密码必须6-20位，包含字母、数字和符号中的至少两种。不能是您之前使用过的密码。',
    remember_password: '想起密码了？',
    back_to_login: '返回登录',
    code_sent_success: '验证码已发送',
    code_sent_failed: '验证码发送失败',
    reset_success: '密码重置成功',
    reset_failed: '密码重置失败',
  },
  home: {
    title: '连接理想网络以',
    title_highlight: '实惠的价格',
    adventure: '冒险绽放，享受20%折扣！',
    share_friend: '邀请朋友，享受3美元优惠！',
    popular: {
      title: '热门目的地',
      local: '本地eSIM卡',
      regional: '区域eSIM卡',
    },
    download_app: {
      title: '下载GLOCALME应用',
      description: '下载GlocalMe应用，随时随地购买、管理和充值您的套餐！',
      app_store: '从App Store下载',
      google_play: '从Google Play获取',
    },
    refer_friend: {
      title: '带上朋友一起',
      highlight: '享受5美元优惠',
      button: '推荐朋友',
    },
    user_reviews: {
      title: '来自GlocalMe用户的评价！',
    },
    how_it_works: {
      title: 'GLOCALME如何工作？',
      steps: {
        step1: {
          step: '步骤01',
          title: '登录',
          description: '安装GlocalMe应用并创建新账户',
        },
        step2: {
          step: '步骤02',
          title: '购买',
          description: '导航至商城 > eSIM和旅行SIM > eSIM标准版。选择您的目的地和套餐',
        },
        step3: {
          step: '步骤03',
          title: '激活',
          description:
            '访问订单详情 > eSIM安装说明获取您的二维码或激活码。确保网络连接并按照步骤完成eSIM激活。',
        },
        step4: {
          step: '步骤04',
          title: '访问',
          description: '为您的新eSIM套餐启用"数据漫游"开关以连接网络',
        },
      },
    },
    exclusive: {
      tag: 'CEO专享',
      title: '100GB 365天',
      subtitle: '全球套餐',
      button: '了解更多',
    },
  },
  footer: {
    start_travel: '开始您的GlocalMe旅程！',
    stay_connected: '保持连接',
    shop_plans: '购买套餐',
    refer_friend: '推荐朋友',
    my_promos: '我的优惠',
    about_us: '关于我们',
    about_glocalme: '关于GlocalMe',
    terms: '条款和条件',
    privacy_policy: '隐私政策',
    contact_us: '联系我们',
    follow_us: '关注我们',
    scan_download_app: '扫码下载应用',
  },
  myEsims: {
    title: '我的eSIM',
    planType: '套餐类型',
    coverage: '覆盖范围',
    expiry: '到期时间',
    details: '详情',
    buyAgain: '再次购买',
    empty: {
      title: '暂无eSIM套餐！',
      description: '购买套餐后将在此处显示。',
      shopPlans: '购买套餐',
    },
    downloadApp: {
      title: '下载GLOCALME应用',
      description: '下载GlocalMe应用，随时随地购买、管理和充值您的套餐！',
    },
  },
  copyright: {
    text: '版权所有@2025 GlocalMe uCloudlink集团有限公司。',
  },
}

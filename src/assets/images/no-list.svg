<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 主球体 -->
  <circle cx="100" cy="100" r="60" fill="#E5E7EB" opacity="0.8"/>
  <circle cx="100" cy="100" r="60" fill="url(#gradient1)" opacity="0.6"/>
  
  <!-- 环形轨道 -->
  <ellipse cx="100" cy="100" rx="80" ry="25" fill="none" stroke="#D1D5DB" stroke-width="3" opacity="0.7"/>
  <ellipse cx="100" cy="100" rx="80" ry="25" fill="none" stroke="url(#gradient2)" stroke-width="2" opacity="0.5"/>
  
  <!-- 小装饰点 -->
  <circle cx="60" cy="80" r="3" fill="#D1D5DB" opacity="0.6"/>
  <circle cx="140" cy="120" r="2" fill="#D1D5DB" opacity="0.4"/>
  <circle cx="80" cy="140" r="2.5" fill="#D1D5DB" opacity="0.5"/>
  <circle cx="120" cy="60" r="2" fill="#D1D5DB" opacity="0.3"/>
  
  <!-- 浮动的小圆点 -->
  <circle cx="50" cy="100" r="1.5" fill="#D1D5DB" opacity="0.4"/>
  <circle cx="150" cy="100" r="1.5" fill="#D1D5DB" opacity="0.4"/>
  <circle cx="100" cy="50" r="1" fill="#D1D5DB" opacity="0.3"/>
  <circle cx="100" cy="150" r="1" fill="#D1D5DB" opacity="0.3"/>
  
  <!-- 渐变定义 -->
  <defs>
    <radialGradient id="gradient1" cx="0.3" cy="0.3" r="0.7">
      <stop offset="0%" stop-color="#F9FAFB"/>
      <stop offset="100%" stop-color="#E5E7EB"/>
    </radialGradient>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#D1D5DB"/>
      <stop offset="50%" stop-color="#9CA3AF"/>
      <stop offset="100%" stop-color="#D1D5DB"/>
    </linearGradient>
  </defs>
</svg>
